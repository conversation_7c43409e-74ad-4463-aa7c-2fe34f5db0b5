package com.dailycodework.shopping_cart.Service.Interface;

import com.dailycodework.shopping_cart.DTO.Dto.ReviewDto;
import com.dailycodework.shopping_cart.DTO.Request.ReviewRequest;
import com.dailycodework.shopping_cart.Entity.Review;

import java.util.List;

public interface IReview {
    ReviewDto createReview (ReviewRequest request);
    List<ReviewDto> getListReviewByProductId (Long productId);
}
