package com.dailycodework.shopping_cart.Repository;

import com.dailycodework.shopping_cart.Entity.CartItem;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CartItemRepository extends JpaRepository<CartItem,Long> {
    @Transactional
    void deleteAllByCartId (Long id);
    List<CartItem> findByCartId (Long id);
}
