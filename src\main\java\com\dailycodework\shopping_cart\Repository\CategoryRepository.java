package com.dailycodework.shopping_cart.Repository;

import com.dailycodework.shopping_cart.Entity.Category;
import com.dailycodework.shopping_cart.Entity.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CategoryRepository extends JpaRepository<Category,Long> {
    Optional<Category> findByName(String name);
    boolean existsByName(String name);
}
