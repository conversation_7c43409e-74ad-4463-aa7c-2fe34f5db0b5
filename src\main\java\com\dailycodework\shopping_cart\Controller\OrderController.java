package com.dailycodework.shopping_cart.Controller;

import com.dailycodework.shopping_cart.DTO.Dto.OrderDto;
import com.dailycodework.shopping_cart.DTO.Response.ApiResponse;
import com.dailycodework.shopping_cart.Entity.Cart;
import com.dailycodework.shopping_cart.Entity.Order;
import com.dailycodework.shopping_cart.Entity.OrderItem;
import com.dailycodework.shopping_cart.Entity.Product;
import com.dailycodework.shopping_cart.Exception.AppException;
import com.dailycodework.shopping_cart.Exception.ErrorCode;
import com.dailycodework.shopping_cart.Service.Interface.IOrder;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;

import static com.dailycodework.shopping_cart.Enum.OderStatus.PENDING;

@RestController
@RequestMapping("/orders")
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class OrderController {
    IOrder orderService;
    @PostMapping("/create/{userId}")
    public ApiResponse<OrderDto> createOrder(@PathVariable Long userId) {
        try {
            OrderDto order = orderService.placeOrder(userId);
            return ApiResponse.<OrderDto>builder()
                    .code(200)
                    .message("create success")
                    .result(order)
                    .build();
        }
        catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/get-order/{orderId}")
    public ApiResponse<OrderDto> getOrderById(@PathVariable Long orderId) {
        return ApiResponse.<OrderDto>builder()
                .code(200)
                .message("get success")
                .result(orderService.getOrder(orderId))
                .build();
    }
    @GetMapping("/history-order/{userId}")
    public ApiResponse<List<OrderDto>> getUserOrders(@PathVariable Long userId) {
        return ApiResponse.<List<OrderDto>>builder()
                .code(200)
                .message("get success")
                .result(orderService.getUserOrders(userId))
                .build();
    }
    @PutMapping("/applyVoucher/{orderId}")
    public ApiResponse<OrderDto> applyVoucher(@PathVariable Long orderId,@RequestParam String voucherCode) {
        return ApiResponse.<OrderDto>builder()
                .code(200)
                .message("apply voucher success")
                .result(orderService.applyVoucher(orderId,voucherCode))
                .build();
    }
    @PostMapping("/applyVoucher/{orderId}")
    public ApiResponse<OrderDto> testApplyVoucher(@PathVariable Long orderId,@RequestParam String voucherCode) {
        return ApiResponse.<OrderDto>builder()
                .code(200)
                .message("Test apply voucher success")
                .result(orderService.testApplyVoucher(orderId,voucherCode))
                .build();
    }
}
