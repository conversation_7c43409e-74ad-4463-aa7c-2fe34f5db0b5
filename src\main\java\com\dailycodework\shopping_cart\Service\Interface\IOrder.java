package com.dailycodework.shopping_cart.Service.Interface;

import com.dailycodework.shopping_cart.DTO.Dto.OrderDto;
import com.dailycodework.shopping_cart.Entity.Order;

import java.util.List;

public interface IOrder {
    OrderDto placeOrder (Long userId);
    OrderDto getOrder (Long orderId);
    List<OrderDto> getUserOrders (Long userId);
    OrderDto applyVoucher(Long orderId, String voucherCode);
    OrderDto testApplyVoucher(Long orderId, String voucherCode);
}
