package com.dailycodework.shopping_cart.DTO.Dto;

import com.dailycodework.shopping_cart.Entity.Order;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VoucherDto {
    Long id;
    String code;
    BigDecimal discountAmount;
    boolean percentTage;
    int usageLimit;
    int usedCount;
    LocalDateTime startDate;
    LocalDateTime endDate;
    boolean active;
    BigDecimal minOrderAmount;      // ✅ Đơn hàng tối thiểu
    BigDecimal maxDiscountAmount;   // ✅ Mức giảm tối đa (rất quan trọng)
}
