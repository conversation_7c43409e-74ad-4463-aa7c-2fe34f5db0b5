package com.dailycodework.shopping_cart.Entity;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Voucher {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;
    String code;
    BigDecimal discountAmount;
    boolean percentTage;
    int usageLimit;
    int usedCount;
    LocalDateTime startDate;
    LocalDateTime endDate;
    boolean active;
    BigDecimal minOrderAmount;      // ✅ Đơn hàng tối thiểu
    BigDecimal maxDiscountAmount;   // ✅ Mức giảm tối đa (rất quan trọng)
}
