package com.dailycodework.shopping_cart.Service.Interface;

import com.dailycodework.shopping_cart.DTO.Dto.OrderDto;
import com.dailycodework.shopping_cart.DTO.Dto.VoucherDto;
import com.dailycodework.shopping_cart.DTO.Request.VoucherRequest;

import java.util.List;
import java.util.Optional;

public interface IVoucher {
    VoucherDto createVoucher (VoucherRequest request);
    VoucherDto getVoucherById (Long id);
    void deleteVoucher(Long id);
    List<VoucherDto> getAllVoucher();
    VoucherDto updateVoucher (Long id, VoucherRequest request);
}
