package com.dailycodework.shopping_cart.Exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

@Getter
public enum ErrorCode {
    PRODUCT_NOT_FOUND (100,"Product not found", HttpStatus.NOT_FOUND),
    CATEGORY_EXISTED (102,"Category existed", HttpStatus.BAD_REQUEST),
    CATEGORY_NOT_FOUND (101,"Category not found", HttpStatus.NOT_FOUND),
    CART_NOT_FOUND (101,"Cart not found", HttpStatus.NOT_FOUND),
    ORDER_NOT_FOUND (101,"Order not found", HttpStatus.NOT_FOUND),
    CART_NOT_EXIST (101,"Cart not exist", HttpStatus.NOT_FOUND),
    CART_ITEM_NOT_EXIST (101,"Cart item not exist", HttpStatus.NOT_FOUND),
    USER_NOT_EXIST (101,"User not exist", HttpStatus.NOT_FOUND),
    EMAIL_EXISTED (101,"Email existed", HttpStatus.CREATED),
    USER_NOT_FOUND (101,"User not found", HttpStatus.NOT_FOUND),
    IMAGE_NOT_FOUND (101,"Image not found", HttpStatus.NOT_FOUND),
    STATUS_ORDER_NOT_SUCCESS (104,"Just rate product when it delivered", HttpStatus.BAD_REQUEST),
    VOUCHER_NOT_EXIST(105,"voucher not found",HttpStatus.NOT_FOUND),
    VOUCHER_INVALID(106,"voucher invalid",HttpStatus.BAD_REQUEST),
    ACCOUNT_NOT_VERIFY(107,"Account not verified. Plaese verify your account", HttpStatus.BAD_REQUEST),
    ACCOUNT_ALREADY_VERIFIED(110,"Account already verified", HttpStatus.BAD_REQUEST),
    INVALID_VERIFICATION_CODE(108,"Invalid verification code", HttpStatus.BAD_REQUEST),
    VERIFICATION_CODE_EXPIRED(109,"Verification code has expired", HttpStatus.BAD_REQUEST),
    USER_EXISTED(111,"User existed", HttpStatus.BAD_REQUEST),
    INVALID_PASSWORD(112,"Old passwords do not correct", HttpStatus.BAD_REQUEST),
    INVALID_CONFIRM_PASSWORD(113,"New and confirm passwords do not match", HttpStatus.BAD_REQUEST),
    TOKEN_RESET_PASSWORD_INVALID(114,"Token reset password expired", HttpStatus.BAD_REQUEST);
    private final int code;
    private final String message;
    private final HttpStatusCode httpStatusCode;
    ErrorCode(int code, String message, HttpStatusCode httpStatusCode){
        this.code=code;
        this.message=message;
        this.httpStatusCode= httpStatusCode;
    }
}
