package com.dailycodework.shopping_cart.Controller;

import com.dailycodework.shopping_cart.DTO.Dto.VoucherDto;
import com.dailycodework.shopping_cart.DTO.Request.VoucherRequest;
import com.dailycodework.shopping_cart.DTO.Response.ApiResponse;
import com.dailycodework.shopping_cart.Entity.Voucher;
import com.dailycodework.shopping_cart.Exception.AppException;
import com.dailycodework.shopping_cart.Exception.ErrorCode;
import com.dailycodework.shopping_cart.Service.Interface.IVoucher;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/voucher")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class VoucherController {
    IVoucher voucherService;
    @PostMapping("/create")
    public ApiResponse<VoucherDto> createVoucher (@RequestBody VoucherRequest request){
        return ApiResponse.<VoucherDto>builder()
                .code(200)
                .message("create success")
                .result(voucherService.createVoucher(request))
                .build();
    }
    @GetMapping("/getVoucher/{id}")
    public ApiResponse<VoucherDto> getVoucherById (@PathVariable Long id){
        return ApiResponse.<VoucherDto>builder()
                .code(200)
                .message("get voucher success")
                .result(voucherService.getVoucherById(id))
                .build();
    }
    @DeleteMapping("/delete/{id}")
    public ApiResponse<Void> deleteVoucher(@PathVariable Long id){
        return ApiResponse.<Void>builder()
                .code(200)
                .message("delete success")
                .build();
    }
    @GetMapping("/getAll")
    public ApiResponse<List<VoucherDto>> getAllVoucher(){
        List<VoucherDto> voucherDtoList = voucherService.getAllVoucher();
        if(voucherDtoList.isEmpty()){
            return ApiResponse.<List<VoucherDto>>builder()
                    .code(200)
                    .message("List voucher is null")
                    .build();
        }
        return ApiResponse.<List<VoucherDto>>builder()
                .code(200)
                .message("get List voucher success")
                .result(voucherDtoList)
                .build();
    }
    @PutMapping("/update/{id}")
    public ApiResponse<VoucherDto> updateVoucher(@PathVariable Long id,@RequestBody VoucherRequest request) {
        return ApiResponse.<VoucherDto>builder()
                .code(200)
                .message("update success")
                .result(voucherService.updateVoucher(id,request))
                .build();
    }
}
