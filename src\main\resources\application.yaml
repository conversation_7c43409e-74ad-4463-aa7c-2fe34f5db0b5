server:
  port: 8080
  servlet:
    context-path: /api
spring:
  datasource:
    url: "*****************************************"
    username: root
    password: root
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: vlsbygdfcwqpyoew
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: 839687066841-pqes2fn1ss9pc9m8eqjpk3cjepe05tc6.apps.googleusercontent.com
            client-secret: 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
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
cloudinary:
  cloud_name: "dwbcqjupj"
  api_key: "875968599478514"
  api_secret: "-DJf06wT8bsapQKY4R77f2P4qUY"
security:
  jwt:
    token:
      secretKey: "UKILXZgtUPJ976SZmgiN4YeADLEnkCpwXOm7k8i54dNV1H68QW9chZVp5r5KBOQ77PPOFURzKeyxEjRR2yHkjk1FrE3PYXUhPckRJpaaJRw="
      expiration: 3600000
      expiration_refresh: 604800000