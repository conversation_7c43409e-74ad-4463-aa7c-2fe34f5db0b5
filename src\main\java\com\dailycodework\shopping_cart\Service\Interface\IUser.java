package com.dailycodework.shopping_cart.Service.Interface;

import com.dailycodework.shopping_cart.DTO.Request.UserRequest;
import com.dailycodework.shopping_cart.DTO.Response.UserResponse;

public interface IUser {
    UserResponse getUser (Long userId);
    UserResponse createUser (UserRequest request);
    UserResponse updateUser(UserRequest request, Long userId);
    void deleteUser (Long userId);

}
