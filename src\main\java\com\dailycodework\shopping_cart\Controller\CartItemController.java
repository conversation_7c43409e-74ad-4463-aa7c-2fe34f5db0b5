package com.dailycodework.shopping_cart.Controller;

import com.dailycodework.shopping_cart.DTO.Dto.CartItemDto;
import com.dailycodework.shopping_cart.DTO.Response.ApiResponse;
import com.dailycodework.shopping_cart.Entity.Cart;
import com.dailycodework.shopping_cart.Entity.CartItem;
import com.dailycodework.shopping_cart.Entity.Product;
import com.dailycodework.shopping_cart.Exception.AppException;
import com.dailycodework.shopping_cart.Exception.ErrorCode;
import com.dailycodework.shopping_cart.Service.Interface.ICart;
import com.dailycodework.shopping_cart.Service.Interface.ICartItem;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/cartItem")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class CartItemController {
    ICartItem cartItemService;
    ICart cartService;
    @PostMapping ("/add-cartItem")
    public ApiResponse<Void> addItemToCart (@RequestParam(required = false) Long cartId, @RequestParam Long productId, @RequestParam int quantity){
        if(cartId == null){
            cartId = cartService.initializeNewCart();
        }
        cartItemService.addItemToCart(cartId,productId,quantity);
        return ApiResponse.<Void>builder()
                .code(200)
                .message("add success")
                .build();
    }
    @PostMapping ("/remove-cartItem")
    public ApiResponse<Cart> removeItemFromCart (@RequestParam Long cartId, @RequestParam Long productId){
        Cart cart = cartItemService.removeItemFromCart(cartId,productId);
        return ApiResponse.<Cart>builder()
                .code(200)
                .message("remove success")
                .result(cart)
                .build();
    }

    @PutMapping("/update-quantity")
    public ApiResponse<CartItemDto> updateItemQuantity (@RequestParam Long cartId, @RequestParam Long productId, @RequestParam int quantity){
        CartItemDto cart = cartItemService.updateItemQuantity(cartId,productId, quantity);
        return ApiResponse.<CartItemDto>builder()
                .code(200)
                .message("update success")
                .result(cart)
                .build();
    }
    @GetMapping("/get-item/{cartId}")
    public ApiResponse<List<CartItemDto>> getItemFromCart (@PathVariable Long cartId){
        List<CartItemDto> cart = cartItemService.getItemFromCart(cartId);
        return ApiResponse.<List<CartItemDto>>builder()
                .code(200)
                .message("get item success")
                .result(cart)
                .build();
    }
    @PatchMapping("/selected/{id}")
    public ApiResponse<CartItemDto> updateSelected(@PathVariable Long id,@RequestParam boolean selected) {
        return ApiResponse.<CartItemDto>builder()
                .code(200)
                .message("update success")
                .result(cartItemService.updateSelected(id,selected))
                .build();
    }
}
