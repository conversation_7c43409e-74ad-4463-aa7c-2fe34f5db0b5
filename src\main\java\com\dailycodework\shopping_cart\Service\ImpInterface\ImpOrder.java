package com.dailycodework.shopping_cart.Service.ImpInterface;

import com.dailycodework.shopping_cart.DTO.Dto.OrderDto;
import com.dailycodework.shopping_cart.Entity.*;
import com.dailycodework.shopping_cart.Exception.AppException;
import com.dailycodework.shopping_cart.Exception.ErrorCode;
import com.dailycodework.shopping_cart.Mapper.OrderMapper;
import com.dailycodework.shopping_cart.Repository.OrderRepository;
import com.dailycodework.shopping_cart.Repository.ProductRepository;
import com.dailycodework.shopping_cart.Repository.VoucherRepository;
import com.dailycodework.shopping_cart.Service.Interface.ICart;
import com.dailycodework.shopping_cart.Service.Interface.IOrder;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dailycodework.shopping_cart.Enum.OderStatus.PENDING;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE,makeFinal = true)
public class ImpOrder implements IOrder {
    OrderRepository orderRepository;
    ProductRepository productRepository;
    ICart cartService;
    OrderMapper orderMapper;
    VoucherRepository voucherRepository;

    @Override
    public OrderDto placeOrder(Long userId) {
        Cart cart = cartService.getCartByUserId(userId);
//        User user = cartService.getUserByUserId(userId);
        Set<CartItem> selectedItems = new HashSet<>(cart.getCartItems().stream().filter(CartItem::isSelected).toList());

        Order order = Order.builder()
                .oderStatus(PENDING)
                .user(cart.getUser())
                .build();
        Set<OrderItem> orderItemSet =  mapCartItemsToOrderItems(selectedItems,order);
        List<OrderItem> orderItemList = createOrderItems(order,cart);
        // chuyển list sang set dùng new hashset<>(...)
//        order.setOrderItems(new HashSet<>(orderItemList));
        order.setOrderItems(orderItemSet);
        order.setTotalAmount(calculateTotalAmount(orderItemSet));
        Order saveOder = orderRepository.save(order);
        cartService.clearSelectedItems(cart.getId());
        return orderMapper.toOrderDto(saveOder);
    }
    private Set<OrderItem> mapCartItemsToOrderItems(Set<CartItem> cartItems, Order order) {
        return cartItems.stream()
                .map(cartItem ->{
                    Product product = cartItem.getProduct();
                    product.setInventory(product.getInventory()-cartItem.getQuantity());
                    return OrderItem.builder()
                        .order(order) // liên kết Order hiện tại
                        .product(cartItem.getProduct())
                        .quantity(cartItem.getQuantity())
                        .price(cartItem.getUnitPrice())
                        .build();
                })
                .collect(Collectors.toSet());
    }
    private List<OrderItem> createOrderItems(Order order, Cart cart){
        return cart.getCartItems().stream().map(cartItem -> {
            Product product = cartItem.getProduct();
            product.setInventory(product.getInventory() - cartItem.getQuantity());
            productRepository.save(product);

            return OrderItem.builder()
                    .order(order)
                    .product(product)
                    .quantity(cartItem.getQuantity())
                    .price(cartItem.getUnitPrice())
                    .build();
        }).toList();


    }

    private List<OrderItem> createOrderItem(Order order, Cart cart){
        return cart.getCartItems().stream().map(cartItem -> {
            Product product = cartItem.getProduct();
            product.setInventory(product.getInventory() - cartItem.getQuantity());
            productRepository.save(product);

            return OrderItem.builder()
                    .order(order)
                    .product(product)
                    .quantity(cartItem.getQuantity())
                    .price(cartItem.getUnitPrice())
                    .build();
        }).toList();

    }

    private BigDecimal calculateTotalAmount (Set<OrderItem> orderItemList){
        return orderItemList.stream().map(orderItem -> orderItem.getPrice().multiply(new BigDecimal(orderItem.getQuantity())))
                .reduce(BigDecimal.ZERO,BigDecimal::add);
    }
    @Override
    public OrderDto getOrder(Long orderId) {
        return orderMapper.toOrderDto(orderRepository.findById(orderId).orElseThrow(()->new AppException(ErrorCode.ORDER_NOT_FOUND)));
    }

    @Override
    public List<OrderDto> getUserOrders(Long userId) {
        return orderMapper.toListOrderDto(orderRepository.findByUserId(userId));
    }

    @Override
    public OrderDto applyVoucher(Long orderId, String voucherCode) {
        Order order = orderRepository.findById(orderId).orElseThrow(()->new AppException(ErrorCode.ORDER_NOT_FOUND));
        Voucher voucher = voucherRepository.findByCode(voucherCode).orElseThrow(()->new AppException(ErrorCode.VOUCHER_NOT_EXIST));
        if(!voucher.isActive()||voucher.getUsageLimit()<=voucher.getUsedCount()||voucher.getStartDate().isAfter(LocalDateTime.now())||voucher.getEndDate().isBefore(LocalDateTime.now())){
            throw new AppException(ErrorCode.VOUCHER_INVALID);
        }
        if (voucher.getMinOrderAmount() !=null && order.getTotalAmount().compareTo(voucher.getMinOrderAmount())<0) {
            throw new RuntimeException("Order amount too low for this voucher");
        }
        BigDecimal discount = voucher.isPercentTage()?order.getTotalAmount().multiply(voucher.getDiscountAmount()).divide(BigDecimal.valueOf(100)): voucher.getDiscountAmount();
        if(voucher.getMaxDiscountAmount()==null){
            discount = discount.min(order.getTotalAmount());
        }
        else discount = discount.min(voucher.getMaxDiscountAmount());
        // Gán voucher vào order
        order.setVoucher(voucher);
        order.setDiscountApplied(discount);
        order.setTotalAmount(order.getTotalAmount().subtract(discount));
        voucher.setUsedCount(voucher.getUsedCount()+1);
        voucherRepository.save(voucher);
        return orderMapper.toOrderDto(orderRepository.save(order));
    }
    @Override
    public OrderDto testApplyVoucher(Long orderId, String voucherCode) {
        Order order = orderRepository.findById(orderId).orElseThrow(()->new AppException(ErrorCode.ORDER_NOT_FOUND));
        Voucher voucher = voucherRepository.findByCode(voucherCode).orElseThrow(()->new AppException(ErrorCode.VOUCHER_NOT_EXIST));
        if(!voucher.isActive()||voucher.getUsageLimit()<=voucher.getUsedCount()||voucher.getStartDate().isAfter(LocalDateTime.now())||voucher.getEndDate().isBefore(LocalDateTime.now())){
            throw new AppException(ErrorCode.VOUCHER_INVALID);
        }
        if (voucher.getMinOrderAmount() !=null && order.getTotalAmount().compareTo(voucher.getMinOrderAmount())<0) {
            throw new RuntimeException("Order amount too low for this voucher");
        }
        BigDecimal discount = voucher.isPercentTage()?order.getTotalAmount().multiply(voucher.getDiscountAmount()).divide(BigDecimal.valueOf(100)): voucher.getDiscountAmount();
        if(voucher.getMaxDiscountAmount()==null){
            discount = discount.min(order.getTotalAmount());
        }
        else discount = discount.min(voucher.getMaxDiscountAmount());
        // Gán voucher vào order
        order.setVoucher(voucher);
        order.setDiscountApplied(discount);
        order.setTotalAmount(order.getTotalAmount().subtract(discount));
        return orderMapper.toOrderDto(order);
    }
}
