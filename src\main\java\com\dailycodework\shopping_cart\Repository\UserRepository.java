package com.dailycodework.shopping_cart.Repository;

import com.dailycodework.shopping_cart.Entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User,Long> {
    boolean existsByEmail (String email);
    boolean existsByUsername (String username);
    Optional<User> findByUsername (String username);
    Optional<User> findByEmail (String email);
    Optional<User> findByResetPasswordToken (String resetPasswordToken);
}
