package com.dailycodework.shopping_cart.Service.ImpInterface;

import com.dailycodework.shopping_cart.DTO.Dto.CartItemDto;
import com.dailycodework.shopping_cart.Entity.Cart;
import com.dailycodework.shopping_cart.Entity.CartItem;
import com.dailycodework.shopping_cart.Entity.Product;
import com.dailycodework.shopping_cart.Exception.AppException;
import com.dailycodework.shopping_cart.Exception.ErrorCode;
import com.dailycodework.shopping_cart.Mapper.CartItemMapper;
import com.dailycodework.shopping_cart.Mapper.ProductMapper;
import com.dailycodework.shopping_cart.Repository.CartItemRepository;
import com.dailycodework.shopping_cart.Repository.CartRepository;
import com.dailycodework.shopping_cart.Service.Interface.ICartItem;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
@Builder
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ImpCartItem implements ICartItem {
    CartItemRepository cartItemRepository;
    CartRepository cartRepository;
    CartItemMapper mapper;
    ImpProduct productService;
    ImpCart cartService;
    boolean selected=false;
    ProductMapper productMapper;
    @Override
    public void addItemToCart(Long cartId, Long productId, int quantity) {
        Cart cart = cartService.getCart(cartId);
        Product product =  productMapper.responseToProduct(productService.getProductById(productId));
        CartItem cartItem = cart.getCartItems().stream().filter(item->item.getProduct().getId().equals(productId))
                .findFirst().orElse(new CartItem());
        if(cartItem.getId()==null){
            cartItem = CartItem.builder()
                    .cart(cart)
                    .product(product)
                    .quantity(quantity)
                    .unitPrice(product.getPrice())
                    .build();
        }
        else{
            cartItem.setQuantity(quantity);
        }
        //update price in cart item
        cartItem.setTotalPrice();
        //add cart item into cart
        cart.getCartItems().add(cartItem);
        //update totalAmount in cart
        BigDecimal totalAmount = cart.updateTotalAmount();
        cart.setTotalAmount(totalAmount);
        cartItem.setCart(cart);
        //save to db
        cartItemRepository.save(cartItem);
        cartRepository.save(cart);
    }

    @Override
    public Cart removeItemFromCart(Long cartId, Long productId) {
        Cart cart = cartService.getCart(cartId);
        CartItem cartItemRemove = cart.getCartItems().stream().filter(item -> item.getProduct().getId().equals(productId)).findFirst().orElseThrow(()-> new AppException(ErrorCode.PRODUCT_NOT_FOUND));

        cart.getCartItems().remove(cartItemRemove);
        cart.updateTotalAmount();
        return cartRepository.save(cart);
    }


    @Override
    public CartItemDto updateItemQuantity(Long cartId, Long productId, int quantity) {
        Cart cart = cartService.getCart(cartId);
        CartItem cartItem = cart.getCartItems().stream().filter(cartItem1 -> cartItem1.getProduct().getId().equals(productId)).findFirst().orElseThrow(()-> new AppException(ErrorCode.PRODUCT_NOT_FOUND));
        cartItem.setQuantity(quantity);
        cartItem.setUnitPrice(cartItem.getProduct().getPrice());
        cartItem.setTotalPrice();
        BigDecimal totalAmount = cart.updateTotalAmount();
        cart.setTotalAmount(totalAmount);
        cartRepository.save(cart);
        return mapper.cartItemDto(cartItem);
    }
    @Override
    public List<CartItemDto> getItemFromCart(Long cartId) {
        return mapper.cartItemsDto(cartItemRepository.findByCartId(cartId));
    }
    @Transactional
    @Override
    public CartItemDto updateSelected(Long id, boolean selected) {
        CartItem cartItem = cartItemRepository.findById(id).orElseThrow(()-> new AppException(ErrorCode.CART_ITEM_NOT_EXIST));
        cartItem.setSelected(selected);
        CartItem updatedCartItem = cartItemRepository.save(cartItem);
        return  mapper.cartItemDto(updatedCartItem);
    }
}
