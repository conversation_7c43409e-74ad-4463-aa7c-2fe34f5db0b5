package com.dailycodework.shopping_cart.Service.ImpInterface;

import com.dailycodework.shopping_cart.DTO.Dto.VoucherDto;
import com.dailycodework.shopping_cart.DTO.Request.VoucherRequest;
import com.dailycodework.shopping_cart.Entity.Voucher;
import com.dailycodework.shopping_cart.Exception.AppException;
import com.dailycodework.shopping_cart.Exception.ErrorCode;
import com.dailycodework.shopping_cart.Mapper.VoucherMapper;
import com.dailycodework.shopping_cart.Repository.VoucherRepository;
import com.dailycodework.shopping_cart.Service.Interface.IVoucher;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE,makeFinal = true)
public class ImpVoucher implements IVoucher {
    ImpOrder orderService;
    VoucherMapper voucherMapper;
    VoucherRepository voucherRepository;
    @Override
    public VoucherDto createVoucher(VoucherRequest request) {
        Voucher voucher = voucherMapper.toVoucher(request);
        voucherRepository.save(voucher);
        return voucherMapper.toVoucherDto(voucher);
    }

    @Override
    public VoucherDto getVoucherById(Long id) {
        return voucherMapper.toVoucherDto(voucherRepository.findById(id).orElseThrow(()->new AppException(ErrorCode.VOUCHER_NOT_EXIST)));
    }

    @Override
    public void deleteVoucher(Long id) {
        voucherRepository.deleteById(id);
    }

    @Override
    public List<VoucherDto> getAllVoucher() {
        return voucherMapper.toListVoucher(voucherRepository.findAll());
    }

    @Override
    public VoucherDto updateVoucher(Long id, VoucherRequest request) {
        Voucher voucher = voucherRepository.findById(id).orElseThrow(()->new AppException(ErrorCode.VOUCHER_NOT_EXIST));
        voucherMapper.updateVoucher(voucher,request);
        return voucherMapper.toVoucherDto(voucherRepository.save(voucher));
    }

}
